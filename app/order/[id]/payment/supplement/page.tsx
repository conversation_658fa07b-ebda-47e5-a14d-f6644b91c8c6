'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams, useSearchParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Navbar } from '@/components/Navbar'
import { formatUSDT } from '@/lib/utils'

interface PaymentMethod {
  id: 'binance_pay' | 'bnb_chain'
  name: string
  description: string
  icon: string
  features: string[]
}

interface Order {
  id: string
  orderNumber: string
  status: string
  totalAmount: number
  productPrice: number
  shippingFee: number
  platformFee: number
  product: {
    id: string
    title: string
    images: string
    seller: {
      id: string
      name: string
    }
  }
}

export default function SupplementPaymentPage() {
  const router = useRouter()
  const params = useParams()
  const searchParams = useSearchParams()
  const { data: session } = useSession()
  
  const [order, setOrder] = useState<Order | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod | null>(null)
  const [processing, setProcessing] = useState(false)

  const orderId = params?.id as string
  const supplementAmount = parseFloat(searchParams?.get('supplementAmount') || '0')
  const useBalance = searchParams?.get('useBalance') === 'true'
  const balanceAmount = parseFloat(searchParams?.get('balanceAmount') || '0')

  const paymentMethods: PaymentMethod[] = [
    {
      id: 'binance_pay',
      name: 'Binance Pay',
      description: '使用币安付款码支付',
      icon: '💳',
      features: ['扫码支付', '即时到账', '低手续费']
    },
    {
      id: 'bnb_chain',
      name: 'BNB Smart Chain',
      description: '直接在BNB链上转账',
      icon: '⛓️',
      features: ['链上交易', '去中心化', '可验证']
    }
  ]

  useEffect(() => {
    if (!session) {
      router.push('/auth/signin')
      return
    }
    
    if (orderId) {
      loadOrderData()
    }
  }, [session, orderId])

  const loadOrderData = async () => {
    try {
      const response = await fetch(`/api/orders/${orderId}`, {
        credentials: 'include'
      })
      
      if (!response.ok) {
        throw new Error('获取订单信息失败')
      }
      
      const orderData = await response.json()
      setOrder(orderData)
      
    } catch (error) {
      console.error('加载订单数据失败:', error)
      alert('加载订单信息失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const handlePayment = async () => {
    if (!selectedMethod || !order) return

    setProcessing(true)

    try {
      const response = await fetch(`/api/orders/${orderId}/payment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          paymentMethod: 'deposit_supplement',
          depositAmount: useBalance ? balanceAmount : 0,
          supplementAmount: supplementAmount,
          supplementMethod: selectedMethod.id
        })
      })
      
      if (response.ok) {
        // 支付成功，跳转到成功页面
        router.push(`/order/${orderId}/payment/successful`)
      } else {
        const error = await response.json()
        alert(error.error || '支付失败')
      }
    } catch (error) {
      console.error('支付失败:', error)
      alert('网络错误，请稍后重试')
    } finally {
      setProcessing(false)
    }
  }

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">请先登录</h2>
          <button
            onClick={() => router.push('/auth/signin')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            去登录
          </button>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (!order) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">订单不存在</h2>
          <button
            onClick={() => router.push('/products')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            返回商品列表
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-2xl mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="text-center mb-6">
            <h1 className="text-2xl font-bold mb-2">补充支付</h1>
            <div className="text-lg text-blue-600 font-semibold">
              需要支付：{formatUSDT(supplementAmount)}
            </div>
            <div className="text-sm text-gray-600 mt-2">
              订单号：{order.orderNumber}
            </div>
          </div>

          {/* 支付明细 */}
          <div className="mb-8">
            <h3 className="font-medium mb-4">支付明细</h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>订单总额</span>
                  <span>{formatUSDT(order.totalAmount)}</span>
                </div>
                {useBalance && (
                  <div className="flex justify-between text-green-600">
                    <span>保证金扣除</span>
                    <span>-{formatUSDT(balanceAmount)}</span>
                  </div>
                )}
                <div className="border-t pt-2 flex justify-between font-semibold text-lg">
                  <span>需要支付</span>
                  <span className="text-blue-600">{formatUSDT(supplementAmount)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* 支付方式选择 */}
          <div className="mb-8">
            <h3 className="font-medium mb-4">选择支付方式</h3>
            <div className="space-y-3">
              {paymentMethods.map((method) => (
                <div
                  key={method.id}
                  className={`border-2 rounded-lg p-4 cursor-pointer transition-all duration-200 ${
                    selectedMethod?.id === method.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300 bg-white'
                  }`}
                  onClick={() => setSelectedMethod(method)}
                >
                  <div className="flex items-start space-x-4">
                    <div className="text-3xl">{method.icon}</div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-lg">{method.name}</h4>
                      <p className="text-gray-600 text-sm mb-2">{method.description}</p>
                      <div className="flex flex-wrap gap-2">
                        {method.features.map((feature, index) => (
                          <span
                            key={index}
                            className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full"
                          >
                            {feature}
                          </span>
                        ))}
                      </div>
                    </div>
                    <div className="flex items-center">
                      <div className={`w-5 h-5 rounded-full border-2 ${
                        selectedMethod?.id === method.id
                          ? 'border-blue-500 bg-blue-500'
                          : 'border-gray-300'
                      }`}>
                        {selectedMethod?.id === method.id && (
                          <div className="w-full h-full rounded-full bg-white scale-50"></div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex space-x-4">
            <button
              onClick={() => router.push(`/order/${orderId}/payment/balance-pay`)}
              className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-3 rounded-md text-lg font-medium"
            >
              返回
            </button>
            
            <button
              onClick={handlePayment}
              disabled={!selectedMethod || processing}
              className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-6 py-3 rounded-md text-lg font-medium"
            >
              {processing ? '处理中...' : `确认支付 ${formatUSDT(supplementAmount)}`}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
