'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Navbar } from '@/components/Navbar'
import { formatUSDT } from '@/lib/utils'

interface Order {
  id: string
  orderNumber: string
  status: string
  totalAmount: number
  productPrice: number
  shippingFee: number
  platformFee: number
  product: {
    id: string
    title: string
    images: string
    seller: {
      id: string
      name: string
    }
  }
  shippingAddress: {
    name: string
    phone: string
    province: string
    city: string
    district: string
    detail: string
  }
  metadata: {
    quantity: number
    variantId?: string
    itemPrice: number
  }
}

export default function BinancePayQRCodePage() {
  const router = useRouter()
  const params = useParams()
  const { data: session } = useSession()
  
  const [order, setOrder] = useState<Order | null>(null)
  const [loading, setLoading] = useState(true)
  const [paymentProcessing, setPaymentProcessing] = useState(false)
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('')
  const [paymentTimeout, setPaymentTimeout] = useState(900) // 15分钟倒计时
  const [pinCode, setPinCode] = useState<string>('')
  const [pinExpiry, setPinExpiry] = useState<Date | null>(null)
  const [userOrderNumber, setUserOrderNumber] = useState<string>('')
  const [pinGenerating, setPinGenerating] = useState(false)

  const orderId = params?.id as string

  useEffect(() => {
    if (!session) {
      router.push('/auth/signin')
      return
    }
    
    if (orderId) {
      loadOrderData()
    }
  }, [session, orderId])

  useEffect(() => {
    // 支付倒计时
    if (paymentTimeout > 0) {
      const timer = setTimeout(() => {
        setPaymentTimeout(paymentTimeout - 1)
      }, 1000)
      return () => clearTimeout(timer)
    } else {
      // 支付超时，返回信息收集页面
      alert('支付超时，请重新选择支付方式')
      router.push(`/order/${orderId}/info-collect`)
    }
  }, [paymentTimeout, orderId, router])

  const loadOrderData = async () => {
    try {
      const response = await fetch(`/api/orders/${orderId}`, {
        credentials: 'include'
      })
      
      if (!response.ok) {
        if (response.status === 404) {
          alert('订单不存在')
          router.push('/products')
          return
        }
        throw new Error('获取订单信息失败')
      }
      
      const orderData = await response.json()
      
      // 检查订单状态
      if (orderData.status !== 'PENDING_PAYMENT') {
        alert('订单状态异常')
        router.push(`/order/${orderId}/info-collect`)
        return
      }
      
      // 检查支付方式
      if (orderData.paymentMethod !== 'binancepay-QRcode') {
        alert('支付方式不匹配')
        router.push(`/order/${orderId}/info-collect`)
        return
      }
      
      setOrder(orderData)
      generateQRCode(orderData)
      // 自动生成PIN码
      generatePin()

    } catch (error) {
      console.error('加载订单数据失败:', error)
      alert('加载订单信息失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const generateQRCode = async (orderData: Order) => {
    try {
      // 这里应该调用币安支付API生成二维码
      // 暂时使用占位符
      setQrCodeUrl('/binance-qr-code.png')
    } catch (error) {
      console.error('生成二维码失败:', error)
      alert('生成支付二维码失败，请稍后重试')
    }
  }

  const generatePin = async () => {
    setPinGenerating(true)
    try {
      const response = await fetch(`/api/orders/${orderId}/generate-pin`, {
        method: 'POST',
        credentials: 'include'
      })

      if (response.ok) {
        const data = await response.json()
        setPinCode(data.pinCode)
        setPinExpiry(new Date(data.expiryTime))
        alert('PIN码生成成功！')
      } else {
        const error = await response.json()
        alert(error.error || 'PIN码生成失败')
      }
    } catch (error) {
      console.error('生成PIN码失败:', error)
      alert('网络错误，请稍后重试')
    } finally {
      setPinGenerating(false)
    }
  }

  const handlePaymentConfirm = async () => {
    // 验证输入
    if (!pinCode) {
      alert('PIN码尚未生成，请稍候')
      return
    }

    if (!userOrderNumber.trim()) {
      alert('请输入币安订单号')
      return
    }

    setPaymentProcessing(true)

    try {
      // 调用PIN码验证API，使用自动生成的PIN码
      const response = await fetch(`/api/orders/${orderId}/verify-pin`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          pin: pinCode.toUpperCase(),
          orderNumber: userOrderNumber
        })
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          alert('支付提交成功，等待管理员审核')
          // 跳转到成功页面
          router.push(`/order/${orderId}/payment/successful`)
        } else {
          alert(result.message || '验证失败')
        }
      } else {
        const error = await response.json()
        alert(error.error || '支付确认失败')
      }
    } catch (error) {
      console.error('支付确认失败:', error)
      alert('网络错误，请稍后重试')
    } finally {
      setPaymentProcessing(false)
    }
  }

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">请先登录</h2>
          <button
            onClick={() => router.push('/auth/signin')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            去登录
          </button>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (!order) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">订单不存在</h2>
          <button
            onClick={() => router.push('/products')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            返回商品列表
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-2xl mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="text-center mb-6">
            <h1 className="text-2xl font-bold mb-2">币安二维码支付</h1>
            <div className="text-lg text-blue-600 font-semibold">
              支付金额：{formatUSDT(order.totalAmount)}
            </div>
            <div className="text-sm text-gray-600 mt-2">
              订单号：{order.orderNumber}
            </div>
          </div>

          {/* 支付倒计时 */}
          <div className="text-center mb-6">
            <div className="inline-flex items-center px-4 py-2 bg-orange-100 text-orange-800 rounded-lg">
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
              </svg>
              请在 {formatTime(paymentTimeout)} 内完成支付
            </div>
          </div>

          {/* 二维码 */}
          <div className="text-center mb-8">
            <div className="inline-block p-4 bg-white border-2 border-gray-200 rounded-lg">
              {qrCodeUrl ? (
                <img
                  src={qrCodeUrl}
                  alt="支付二维码"
                  className="w-64 h-64 mx-auto"
                />
              ) : (
                <div className="w-64 h-64 flex items-center justify-center bg-gray-100 text-gray-500">
                  生成二维码中...
                </div>
              )}
            </div>
            <p className="text-sm text-gray-600 mt-4">
              请使用币安App扫描上方二维码完成支付
            </p>
          </div>

          {/* PIN码管理 */}
          <div className="mb-8">
            <h3 className="font-medium mb-3">支付PIN码</h3>
            <div className="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
              {!pinCode ? (
                <div className="text-center">
                  <p className="text-sm text-gray-600 mb-4">
                    正在自动生成支付PIN码...
                  </p>
                  {pinGenerating && (
                    <div className="text-yellow-600">生成中...</div>
                  )}
                </div>
              ) : (
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-sm font-medium text-yellow-800">您的PIN码：</span>
                    <span className="text-2xl font-mono font-bold text-yellow-900 bg-white px-4 py-2 rounded border">
                      {pinCode}
                    </span>
                  </div>
                  <p className="text-xs text-yellow-700">
                    PIN码有效期：{pinExpiry ? new Date(pinExpiry).toLocaleString() : '未知'}
                  </p>
                  <p className="text-xs text-yellow-700 mt-1">
                    此PIN码将自动用于支付验证，无需手动输入
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* 支付验证 */}
          <div className="mb-8">
            <h3 className="font-medium mb-3">支付验证</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  币安订单号 *
                </label>
                <input
                  type="text"
                  value={userOrderNumber}
                  onChange={(e) => setUserOrderNumber(e.target.value)}
                  placeholder="请输入币安支付完成后的订单号"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <p className="text-xs text-gray-500 mt-1">
                  完成币安支付后，请将订单号填入此处
                </p>
              </div>
            </div>
          </div>

          {/* 支付说明 */}
          <div className="mb-8">
            <h3 className="font-medium mb-3">支付说明</h3>
            <div className="bg-blue-50 rounded-lg p-4">
              <ol className="list-decimal list-inside space-y-2 text-sm text-gray-700">
                <li>系统已自动生成支付PIN码</li>
                <li>打开币安App，点击"扫一扫"</li>
                <li>扫描上方二维码</li>
                <li>确认支付金额并完成支付</li>
                <li>记录币安支付完成后的订单号</li>
                <li>在下方输入币安订单号</li>
                <li>点击"确认支付"按钮提交验证</li>
                <li>等待管理员审核确认</li>
              </ol>
            </div>
          </div>

          {/* 订单信息 */}
          <div className="mb-8">
            <h3 className="font-medium mb-3">订单信息</h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center space-x-4 mb-4">
                <img
                  src={order.product.images?.split(',')[0] || '/placeholder.jpg'}
                  alt={order.product.title}
                  className="w-16 h-16 object-cover rounded"
                />
                <div className="flex-1">
                  <h4 className="font-medium">{order.product.title}</h4>
                  <p className="text-gray-600 text-sm">卖家：{order.product.seller.name}</p>
                  <p className="text-gray-600 text-sm">数量：{order.metadata.quantity}</p>
                </div>
                <div className="text-right">
                  <p className="font-semibold">{formatUSDT(order.productPrice)}</p>
                </div>
              </div>
              
              <div className="border-t pt-4">
                <div className="flex justify-between text-sm mb-1">
                  <span>商品金额</span>
                  <span>{formatUSDT(order.productPrice)}</span>
                </div>
                <div className="flex justify-between text-sm mb-1">
                  <span>运费</span>
                  <span>{formatUSDT(order.shippingFee)}</span>
                </div>
                <div className="flex justify-between font-semibold text-lg pt-2 border-t">
                  <span>总计</span>
                  <span className="text-blue-600">{formatUSDT(order.totalAmount)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex space-x-4">
            <button
              onClick={() => router.push(`/order/${orderId}/info-collect`)}
              className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-3 rounded-md text-lg font-medium"
            >
              返回
            </button>
            <button
              onClick={handlePaymentConfirm}
              disabled={paymentProcessing || !pinCode || !userOrderNumber.trim()}
              className="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-md text-lg font-medium"
            >
              {paymentProcessing ? '验证中...' : '确认支付'}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
